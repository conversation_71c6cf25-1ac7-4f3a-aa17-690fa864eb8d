---
id: quick-start-example
title: Quick Start Examples
sidebar_label: Quick Start Examples
sidebar_position: 5
---

# Installation

First, install the required packages using your preferred package manager:

```bash
# Using npm
npm install @cloc/atoms @cloc/ui @cloc/types

# Using yarn
yarn add @cloc/atoms @cloc/ui @cloc/types

```

## Basic Usage

The Cloc library provides various timer components that can be easily integrated into your React/Next.js applications (compatible with React 18+ and Next.js 13+). Here's a basic example:

```tsx
import { ModernCloc, ClocProvider } from "@cloc/atoms";
import "@cloc/atoms/styles.css";
import "@cloc/ui/styles.css";

export default function App() {
	return (
		<ClocProvider>
			{/* Basic Timer - Compact view with progress indicator */}
			<ModernCloc expanded={false} showProgress variant="default" />

			{/* Expanded Timer - Shows additional controls and bordered style */}
			<ModernCloc showProgress variant="bordered" size="default" expanded />

			{/* Resizable Timer - User can adjust the component size */}
			<ModernCloc showProgress variant="default" resizable expanded />
		</ClocProvider>
	);
}
```

The `ModernCloc` component offers various customization options:

- `expanded`: Toggle between compact and expanded views
- `showProgress`: Display a progress indicator
- `variant`: Choose between "default" and "bordered" styles
- `size`: Available in "sm", "default", and "lg" sizes
- `resizable`: Allow users to resize the component

## Data Visualization

Cloc provides powerful charting capabilities through the `BasicClocReport` component:

```tsx
import { BasicClocReport } from "@cloc/atoms";

export default function ChartExample() {
	return (
		<div className="flex gap-4">
			{/* Vertical Bar Chart */}
			<BasicClocReport type="bar-vertical" />

			{/* Line Chart for Time Series */}
			<BasicClocReport type="line" />

			{/* Area Chart for Cumulative Data */}
			<BasicClocReport type="area" />

			{/* Radar Chart for Multi-dimensional Data */}
			<BasicClocReport type="radar" />
		</div>
	);
}
```

The `BasicClocReport` component supports multiple chart types:

- `bar-vertical` and `bar`: For comparing values across categories
- `line`: Perfect for showing trends over time
- `area`: Ideal for displaying cumulative data
- `radar`: Visualize multi-dimensional data
- `tooltip`: Interactive charts with detailed information
- `radial`: Circular progress visualization
- `pie`: Distribution analysis

## [Builder.io](http://builder.io/) Integration

Cloc components can be seamlessly integrated with [Builder.io](http://builder.io/) for visual page building:

```tsx
import { Builder } from "@builder.io/react";
import { ModernCloc } from "@cloc/atoms";

Builder.registerComponent(ModernCloc, {
	name: "Modern Timer",
	inputs: [
		{ name: "expanded", type: "boolean", defaultValue: false },
		{ name: "showProgress", type: "boolean", defaultValue: true },
		{ name: "variant", type: "string", defaultValue: "default" },
		{ name: "size", type: "string", defaultValue: "default" },
	],
});
```

## Theme Support

Cloc supports dark/light theme switching out of the box:

```tsx
import { ClocProvider, ClocThemeToggle } from "@cloc/atoms";

export default function App() {
	return (
		<ClocProvider>
			{/* Add theme toggle button */}
			<ClocThemeToggle />

			{/* Your components automatically respond to theme changes */}
			<ModernCloc expanded={false} showProgress />
		</ClocProvider>
	);
}
```

The `ClocProvider` handles theme management and provides necessary context for all Cloc components. The `ClocThemeToggle` component offers a simple way for users to switch between light and dark themes.

## Component Variants

Cloc offers multiple timer variants to suit different needs:

```tsx
import { ModernCloc, ClocBasic, BasicTimer } from "@cloc/atoms";

export default function TimerVariants() {
	return (
		<div className="flex gap-4">
			{/* Modern design with advanced features */}
			<ModernCloc showProgress variant="default" size="sm" />

			{/* Basic timer with essential functionality */}
			<ClocBasic className="w-fit max-w-[300px]" />

			{/* Classic timer with customizable styling */}
			<BasicTimer background="primary" border="thick" color="secondary" />
		</div>
	);
}
```

Each variant offers different levels of functionality and styling options to match your application's needs.
